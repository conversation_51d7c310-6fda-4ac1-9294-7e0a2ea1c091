from django.contrib.auth.management.commands.createsuperuser import Command as SuperUserCommand
from django.core.management.base import CommandError
import getpass
import os
import sys

class Command(SuperUserCommand):
    help = 'Create a superuser with a username derived from email if not provided'
    
    def handle(self, *args, **options):
        username = options[self.UserModel.USERNAME_FIELD]
        email = options.get('email')
        database = options['database']
        
        # If running non-interactively, ensure username is set
        if not options['interactive']:
            if not username and not email:
                raise CommandError(
                    "You must use --%s or --email with --noinput." 
                    % self.UserModel.USERNAME_FIELD
                )
            elif not username and email:
                # Generate username from email
                username = email.split('@')[0]
                # Make sure username is unique
                base_username = username
                counter = 1
                while self.UserModel._default_manager.db_manager(database).filter(
                    **{self.UserModel.USERNAME_FIELD: username}
                ).exists():
                    username = f"{base_username}{counter}"
                    counter += 1
                options[self.UserModel.USERNAME_FIELD] = username
        
        return super().handle(*args, **options)