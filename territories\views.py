from rest_framework import viewsets
from .models import Region, Area, Territory
from .serializers import RegionSerializer, AreaSerializer, TerritorySerializer
from drf_yasg.utils import swagger_auto_schema

class RegionViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing regions."""
    queryset = Region.objects.all()
    serializer_class = RegionSerializer

class AreaViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing areas."""
    queryset = Area.objects.all()
    serializer_class = AreaSerializer

class TerritoryViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing territories."""
    queryset = Territory.objects.all()
    serializer_class = TerritorySerializer
