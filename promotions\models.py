from django.db import models
from django.utils import timezone
from users.models import User
from brands.models import Brand, SKU
from territories.models import Territory

class Scheme(models.Model):
    """Promotional schemes/campaigns"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    
    # Relationships
    brands = models.ManyToManyField(Brand, related_name='schemes', blank=True)
    skus = models.ManyToManyField(SKU, related_name='schemes', blank=True)
    territories = models.ManyToManyField(Territory, related_name='schemes', blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name

class SchemeTarget(models.Model):
    """Targets for a scheme per customer"""
    scheme = models.ForeignKey(Scheme, on_delete=models.CASCADE, related_name='targets')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scheme_targets')
    target_amount = models.DecimalField(max_digits=12, decimal_places=2)
    achieved_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    achievement_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('scheme', 'customer')
    
    def __str__(self):
        return f"{self.customer.email} - {self.scheme.name}"

class SchemeRegistration(models.Model):
    """Customer registrations for schemes"""
    scheme = models.ForeignKey(Scheme, on_delete=models.CASCADE, related_name='registrations')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scheme_registrations')
    registration_date = models.DateField()
    status = models.CharField(max_length=20, choices=[('PENDING','Pending'),('APPROVED','Approved'),('REJECTED','Rejected')], default='PENDING')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('scheme', 'customer')
    
    def __str__(self):
        return f"{self.customer.email} - {self.scheme.name}"

class Transaction(models.Model):
    """Sales transactions for scheme tracking"""
    invoice_number = models.CharField(max_length=100, unique=True)
    invoice_date = models.DateField()
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transactions')
    scheme = models.ForeignKey(Scheme, on_delete=models.CASCADE, related_name='transactions')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=[('PENDING','Pending'),('APPROVED','Approved'),('REJECTED','Rejected')], default='PENDING')
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_transactions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.invoice_number} - {self.customer.email}"

class TransactionItem(models.Model):
    """Individual items in a transaction"""
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='items')
    sku = models.ForeignKey(SKU, on_delete=models.CASCADE, related_name='transaction_items')
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=12, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.sku.code} - {self.quantity} units"

class Reward(models.Model):
    """Rewards earned by customers for achieving scheme targets"""
    scheme = models.ForeignKey(Scheme, on_delete=models.CASCADE, related_name='rewards')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='rewards')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=10, choices=[('PENDING','Pending'),('APPROVED','Approved'),('PAID','Paid'),('REJECTED','Rejected')], default='PENDING')
    approval_date = models.DateField(null=True, blank=True)
    payment_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.customer.email} - {self.scheme.name} - {self.amount}"
