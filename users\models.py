from django.db import models
from django.contrib.auth.models import Abstract<PERSON>ser, BaseUserManager
from territories.models import Region, Area, Territory

class UserRole(models.Model):
    """Defines the role of a user in the system"""
    ROLE_CHOICES = [
        ('ADMI<PERSON>', 'Administrator'),
        ('NATIONAL', 'National Manager'),
        ('REGIONAL', 'Regional Manager'),
        ('AREA', 'Area Manager'),
        ('TERRITORY', 'Territory Manager'),
        ('NORMAL', 'Normal User'),
    ]
    
    name = models.CharField(max_length=20, choices=ROLE_CHOICES, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.get_name_display()

class CustomUserManager(BaseUserManager):
    def create_user(self, username, password=None, **extra_fields):
        if not username:
            raise ValueError('The Username field must be set')
        user = self.model(username=username, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, username, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        
        return self.create_user(username, password, **extra_fields)

class User(AbstractUser):
    """Custom user model with role-based access control"""
    email = models.EmailField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True)
    role = models.ForeignKey(UserRole, on_delete=models.PROTECT, related_name='users', null=True, blank=True)
    
    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = []
    
    objects = CustomUserManager()
    
    def __str__(self):
        return self.username

class Distributor(models.Model):
    """Distributor model to manage distributors"""
    distributor_name = models.CharField(max_length=100)
    distributor_code = models.CharField(max_length=50, unique=True)
    distributor_territory_name = models.CharField(max_length=100)
    distributor_owner_name = models.CharField(max_length=100)
    territory_manager = models.CharField(max_length=100)

    # Link to territory for geographical mapping
    territory = models.ForeignKey(Territory, on_delete=models.PROTECT, related_name='distributors', null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.distributor_code} - {self.distributor_name}"

class DSR(models.Model):
    """District Sales Representative (Salesman) model"""
    name = models.CharField(max_length=100)
    employee_code = models.CharField(max_length=50, unique=True)
    phone_number = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)

    # Link to distributor
    distributor = models.ForeignKey(Distributor, on_delete=models.CASCADE, related_name='dsrs')

    # Link to territory for geographical mapping
    territory = models.ForeignKey(Territory, on_delete=models.PROTECT, related_name='dsrs', null=True, blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee_code} - {self.name}"

class Customer(User):
    CHANNEL_CHOICES = [
        ('OMLS', 'OMLS'),
        ('NLS', 'NLS'),
        ('WHS', 'WHS'),
        ('SUB D', 'SUB D'),
        ('SUPERMARKET', 'SUPERMARKET'),
    ]
    CUSTOMER_TYPE_CHOICES = [
        ('gold', 'Gold'),
        ('silver', 'Silver'),
        ('diamond', 'Diamond'),
    ]
    customer_code = models.CharField(max_length=50, unique=True)
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)
    physical_address = models.TextField(blank=True)
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPE_CHOICES)

    # New relationships
    distributor = models.ForeignKey(Distributor, on_delete=models.PROTECT, related_name='customers', null=True, blank=True)
    dsr = models.ForeignKey(DSR, on_delete=models.PROTECT, related_name='customers', null=True, blank=True)

    def __str__(self):
        return f"{self.customer_code} - {self.username}"

class PhoneOTP(models.Model):
    phone_number = models.CharField(max_length=20)
    otp = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    is_verified = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.phone_number} - {self.otp}"

