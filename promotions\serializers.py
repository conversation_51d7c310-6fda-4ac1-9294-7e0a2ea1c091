from rest_framework import serializers
from .models import Scheme, SchemeTarget, SchemeRegistration, Transaction, TransactionItem, Reward

class SchemeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Scheme
        fields = '__all__'

class SchemeTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = SchemeTarget
        fields = '__all__'

class SchemeRegistrationSerializer(serializers.ModelSerializer):
    class Meta:
        model = SchemeRegistration
        fields = '__all__'

class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = '__all__'

class TransactionItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = TransactionItem
        fields = '__all__'

class RewardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reward
        fields = '__all__'
