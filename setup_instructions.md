# Project Setup Instructions

## 1. Install requirements
```bash
pip install -r requirements.txt
```

## 2. Create Django project
```bash
django-admin startproject innov8
cd innov8
```

## 3. Create Django apps
```bash
python manage.py startapp users
python manage.py startapp brands
python manage.py startapp promotions
python manage.py startapp territories
```

## 4. Configure settings.py
Update the settings.py file with database configuration, installed apps, etc.

## 5. Create .env file
Create a .env file in the project root with your environment variables

## 6. Run migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

## 7. Create superuser
```bash
python manage.py createsuperuser
```

## 8. Run the server
```bash
python manage.py runserver
```
