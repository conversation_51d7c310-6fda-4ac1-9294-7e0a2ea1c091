# Admin Portal Credentials & Setup Guide

## 🔐 Admin Login Credentials

### Primary Admin Users
Use these credentials to login to the frontend admin portal:

#### Super Administrator
- **Username:** `superadmin`
- **Email:** `<EMAIL>`
- **Password:** `Admin@123`
- **Role:** Administrator
- **Permissions:** Full system access, can manage all users and data

#### System Admin
- **Username:** `admin`
- **Email:** `<EMAIL>`
- **Password:** `Admin@456`
- **Role:** Administrator
- **Permissions:** Administrative access, can manage users and data

#### National Manager
- **Username:** `national_manager`
- **Email:** `<EMAIL>`
- **Password:** `National@789`
- **Role:** National Manager
- **Permissions:** National level management access

#### Regional Manager
- **Username:** `regional_manager`
- **Email:** `<EMAIL>`
- **Password:** `Regional@101`
- **Role:** Regional Manager
- **Permissions:** Regional level management access

## 🌐 Portal URLs

### Admin Interfaces
- **Django Admin Panel:** http://localhost:8000/admin/
- **API Documentation (Swagger):** http://localhost:8000/swagger/
- **API Root Endpoint:** http://localhost:8000/api/

### API Endpoints for Frontend
- **Users:** http://localhost:8000/api/users/
- **Customers:** http://localhost:8000/api/users/customers/
- **Distributors:** http://localhost:8000/api/users/distributors/
- **DSRs:** http://localhost:8000/api/users/dsrs/
- **Territories:** http://localhost:8000/api/territories/
- **Brands:** http://localhost:8000/api/brands/
- **Promotions:** http://localhost:8000/api/promotions/

## 👥 Sample Customer Data

### Test Customers (for frontend testing)
All sample customers use password: `Customer@123`

#### Customer 1: Premium Supermarket
- **Username:** `shoprite_vi`
- **Email:** `<EMAIL>`
- **Customer Code:** `CUST001`
- **Channel:** SUPERMARKET
- **Type:** Diamond
- **Distributor:** Lagos Premium Distributors Ltd
- **DSR:** Tunde Bakare
- **Location:** Victoria Island, Lagos

#### Customer 2: Standard Supermarket
- **Username:** `spar_ikeja`
- **Email:** `<EMAIL>`
- **Customer Code:** `CUST002`
- **Channel:** SUPERMARKET
- **Type:** Gold
- **Distributor:** Mainland Distribution Network
- **DSR:** Ngozi Okwu
- **Location:** Ikeja, Lagos

#### Customer 3: Local Store
- **Username:** `mama_cass_store`
- **Email:** `<EMAIL>`
- **Customer Code:** `CUST003`
- **Channel:** NLS (Neighborhood Local Store)
- **Type:** Silver
- **Distributor:** Abuja Central Distributors
- **DSR:** Yusuf Garba
- **Location:** Surulere, Lagos

## 🏢 Sample Business Data

### Distributors Created
1. **Lagos Premium Distributors Ltd** (LPD001)
   - Territory: Lagos Island
   - Owner: Adebayo Johnson
   - Manager: Funmi Adeyemi

2. **Mainland Distribution Network** (MDN002)
   - Territory: Lagos Mainland
   - Owner: Chidi Okafor
   - Manager: Kemi Ogundimu

3. **Abuja Central Distributors** (ACD003)
   - Territory: FCT Central
   - Owner: Ibrahim Musa
   - Manager: Aisha Mohammed

### DSRs (District Sales Representatives)
- **DSR001:** Tunde Bakare (Lagos Premium Distributors)
- **DSR002:** Ngozi Okwu (Mainland Distribution Network)
- **DSR003:** Yusuf Garba (Abuja Central Distributors)
- **DSR004:** Blessing Eze (Lagos Premium Distributors)
- **DSR005:** Emeka Nwosu (Mainland Distribution Network)
- **DSR006:** Fatima Aliyu (Abuja Central Distributors)
- **DSR007:** Segun Adebisi (Lagos Premium Distributors)
- **DSR008:** Chioma Obi (Mainland Distribution Network)

### Territory Structure
```
Lagos Region (LG)
├── Lagos Island Area (LI)
│   ├── Victoria Island Territory (VI)
│   └── Ikoyi Territory (IK)
└── Lagos Mainland Area (LM)
    ├── Ikeja Territory (IJ)
    └── Surulere Territory (SU)

Abuja Region (AB)
└── Abuja Central Area (AC)
    └── Wuse Territory (WU)

Kano Region (KN)
└── Kano Central Area (KC)
    └── Sabon Gari Territory (SG)
```

## 🛠️ Management Commands

### Seeding Commands
```bash
# Seed admin users and sample data
python manage.py seed_admin_users

# Reset and recreate admin users
python manage.py seed_admin_users --reset

# Display current credentials
python manage.py show_admin_credentials

# Seed general dummy data
python manage.py seed_dummy_data
```

### Server Commands
```bash
# Start development server
python manage.py runserver

# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser manually
python manage.py createsuperuser
```

## 🔧 Frontend Integration Notes

### Authentication
- Use Django's session-based authentication for admin users
- API endpoints support both session and token authentication
- Admin users have `is_staff=True` for admin panel access

### User Roles & Permissions
- **ADMIN:** Full system access
- **NATIONAL:** National level management
- **REGIONAL:** Regional level management
- **AREA:** Area level management
- **TERRITORY:** Territory level management
- **NORMAL:** Basic user access

### Data Relationships
- Customers can be assigned to Distributors and DSRs
- DSRs belong to specific Distributors
- All entities can be linked to Territories for geographical organization
- Use the territory hierarchy for filtering and organization

## 🚨 Security Notes

### Production Considerations
1. **Change all default passwords** before deploying to production
2. **Use environment variables** for sensitive credentials
3. **Enable HTTPS** for all admin interfaces
4. **Implement proper session management**
5. **Add rate limiting** for API endpoints
6. **Use strong password policies**

### Password Policy Recommendations
- Minimum 8 characters
- Include uppercase, lowercase, numbers, and special characters
- Regular password rotation
- Two-factor authentication for admin accounts

## 📞 Support

For technical support or questions about the admin portal setup, contact the development team.

---
**Last Updated:** July 17, 2025
**Version:** 1.0
