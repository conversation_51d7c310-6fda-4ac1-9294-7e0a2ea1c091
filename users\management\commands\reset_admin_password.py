from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model, authenticate

User = get_user_model()

class Command(BaseCommand):
    help = 'Reset admin password and test login'

    def handle(self, *args, **options):
        self.stdout.write('Resetting admin passwords...')
        
        # Reset admin password
        try:
            admin_user = User.objects.get(username='admin')
            admin_user.set_password('admin123')
            admin_user.is_active = True
            admin_user.is_staff = True
            admin_user.save()
            self.stdout.write(f'✓ Reset password for: {admin_user.username}')
        except User.DoesNotExist:
            self.stdout.write('✗ Admin user not found')
        
        # Reset superadmin password
        try:
            superadmin_user = User.objects.get(username='superadmin')
            superadmin_user.set_password('admin123')
            superadmin_user.is_active = True
            superadmin_user.is_staff = True
            superadmin_user.is_superuser = True
            superadmin_user.save()
            self.stdout.write(f'✓ Reset password for: {superadmin_user.username}')
        except User.DoesNotExist:
            self.stdout.write('✗ Superadmin user not found')
        
        # Test authentication
        self.stdout.write('\nTesting authentication...')
        
        test_users = ['admin', 'superadmin']
        for username in test_users:
            try:
                user = authenticate(username=username, password='admin123')
                if user:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'✓ Authentication successful for {username} - '
                            f'is_staff: {user.is_staff}, is_active: {user.is_active}'
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'✗ Authentication failed for {username}')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Error testing {username}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS('\nPassword reset complete! Use password: admin123')
        )
