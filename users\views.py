from rest_framework import viewsets, status, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny, IsAuthenticated
import pandas as pd
import random
from django.utils import timezone
from django.contrib.auth import login, logout, authenticate
from django.middleware.csrf import get_token
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema

from .models import User, UserRole, PhoneOTP, Customer, Distributor, DSR
from .serializers import (
    UserSerializer, UserRoleSerializer,
    PhoneOTPRequestSerializer, PhoneOTPVerifySerializer,
    CustomerSerializer, DistributorSerializer, DSRSerializer
)

class UserRoleViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing user roles."""
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer

class UserViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing users."""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    parser_classes = [MultiPartParser]

    @swagger_auto_schema(operation_description="Upload users in bulk from Excel file.")
    @action(detail=False, methods=['post'], url_path='upload-excel')
    def upload_excel(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file uploaded.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            df = pd.read_excel(file)
            for _, row in df.iterrows():
                role = UserRole.objects.get(name=row['role'])
                User.objects.update_or_create(
                    email=row['email'],
                    defaults={
                        'username': row.get('username', row['email'].split('@')[0]),
                        'first_name': row.get('first_name', ''),
                        'last_name': row.get('last_name', ''),
                        'phone_number': row.get('phone_number', ''),
                        'role': role,
                        'is_active': row.get('is_active', True),
                        'is_staff': row.get('is_staff', False)
                    }
                )
            return Response({'status': 'Users uploaded successfully.'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

def send_otp_sms(phone_number, otp):
    # Integrate with real SMS provider here
    print(f"Sending OTP {otp} to {phone_number}")

class PhoneOTPRequestView(APIView):
    """API endpoint to request OTP for phone verification."""
    permission_classes = [AllowAny]
    @swagger_auto_schema(request_body=PhoneOTPRequestSerializer, responses={200: 'OTP sent.'})
    def post(self, request):
        serializer = PhoneOTPRequestSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = serializer.validated_data['phone_number']
            otp = f"{random.randint(100000, 999999)}"
            PhoneOTP.objects.create(phone_number=phone_number, otp=otp)
            send_otp_sms(phone_number, otp)
            return Response({'detail': 'OTP sent.'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PhoneOTPVerifyView(APIView):
    """API endpoint to verify OTP for phone verification."""
    permission_classes = [AllowAny]
    @swagger_auto_schema(request_body=PhoneOTPVerifySerializer, responses={200: 'OTP verified.'})
    def post(self, request):
        serializer = PhoneOTPVerifySerializer(data=request.data)
        if serializer.is_valid():
            phone_number = serializer.validated_data['phone_number']
            otp = serializer.validated_data['otp']
            try:
                otp_obj = PhoneOTP.objects.filter(phone_number=phone_number, otp=otp, is_verified=False).latest('created_at')
            except PhoneOTP.DoesNotExist:
                return Response({'detail': 'Invalid OTP.'}, status=status.HTTP_400_BAD_REQUEST)
            # Optionally check OTP expiry here
            otp_obj.is_verified = True
            otp_obj.save()
            try:
                user = User.objects.get(phone_number=phone_number)
            except User.DoesNotExist:
                return Response({'detail': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)
            login(request, user)
            return Response({'detail': 'Login successful', 'role': user.role.name if user.role else None}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class DistributorViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing distributors."""
    queryset = Distributor.objects.all()
    serializer_class = DistributorSerializer

class DSRViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing DSRs (District Sales Representatives)."""
    queryset = DSR.objects.all()
    serializer_class = DSRSerializer

class CustomerViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing customers."""
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer

# Authentication Views
class LoginView(APIView):
    """API endpoint for user login"""
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        request_body=serializers.Serializer,
        responses={200: 'Login successful', 400: 'Invalid credentials'}
    )
    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        if not username or not password:
            return Response(
                {'error': 'Username and password are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = authenticate(request, username=username, password=password)

        if user is not None:
            if user.is_staff:  # Only allow staff users to login to admin
                login(request, user)
                return Response({
                    'success': True,
                    'message': 'Login successful',
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'role': {
                            'name': user.role.name if user.role else None,
                            'display_name': user.role.get_name_display() if user.role else None
                        } if user.role else None
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Access denied. Admin privileges required.'},
                    status=status.HTTP_403_FORBIDDEN
                )
        else:
            return Response(
                {'error': 'Invalid credentials'},
                status=status.HTTP_401_UNAUTHORIZED
            )

class LogoutView(APIView):
    """API endpoint for user logout"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        logout(request)
        return Response(
            {'success': True, 'message': 'Logout successful'},
            status=status.HTTP_200_OK
        )

class CSRFTokenView(APIView):
    """API endpoint to get CSRF token"""
    permission_classes = [AllowAny]

    def get(self, request):
        token = get_token(request)
        return Response({'csrfToken': token})

class CurrentUserView(APIView):
    """API endpoint to get current user info"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        return Response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'role': {
                'name': user.role.name if user.role else None,
                'display_name': user.role.get_name_display() if user.role else None
            } if user.role else None
        })
