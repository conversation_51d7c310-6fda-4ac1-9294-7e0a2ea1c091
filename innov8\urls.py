"""
URL configuration for innov8 project.
"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from innov8.admin import admin_site
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

schema_view = get_schema_view(
    openapi.Info(
        title="Innov8 API",
        default_version='v1',
        description="API documentation for Innov8 project",
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path('admin/', admin_site.urls),
    path('api-auth/', include('rest_framework.urls')),
    # Add your app URLs here
    path('api/users/', include('users.urls')),
    path('api/territories/', include('territories.urls')),
    path('api/brands/', include('brands.urls')),
    path('api/promotions/', include('promotions.urls')),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
