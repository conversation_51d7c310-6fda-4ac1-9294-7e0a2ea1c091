from django.db import models
from django.utils import timezone

class Brand(models.Model):
    """Brands like CLOSEUP, PEPSODENT, KNORR, etc."""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True, default='BRANDCODE')
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='brands/logos/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class SKU(models.Model):
    """Stock Keeping Units for each brand"""
    name = models.CharField(max_length=100, default='SKU Name')
    code = models.CharField(max_length=50, unique=True, default='SKUCODE')
    description = models.TextField(blank=True)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name='skus')
    image = models.ImageField(upload_to='skus/images/', blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    unit = models.CharField(max_length=20, default='unit')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.name}"
