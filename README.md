# Project Innov8 Backend API

## Getting Started

This repository contains the backend API for Project Innov8, a Django-based application for managing promotions, territories, brands, and users.

### Prerequisites

- Python 3.8+
- PostgreSQL
- Git

## Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/project-innov8-backend-api.git
cd project-innov8-backend-api
```

### 2. Create a Virtual Environment

```bash
python -m venv venv
```

#### Activate the virtual environment:

**Windows:**
```bash
venv\Scripts\activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure Environment Variables

Create a `.env` file in the project root with the following variables:

```
DEBUG=True
SECRET_KEY=your_secret_key
DATABASE_URL=postgres://user:password@localhost:5432/innov8db
```

### 5. Run Migrations

```bash
python manage.py migrate
```

### 6. Create a Superuser

```bash
python manage.py createsuperuser
```

### 7. Run the Development Server

```bash
python manage.py runserver
```

## Contribution Guidelines

### Creating a New Branch

When working on a new feature or bug fix, always create a new branch. Follow these steps:

1. Make sure you're on the main branch and it's up to date:

```bash
git checkout main
git pull origin main
```

2. Create a new branch with your firstname and the feature you're working on:

```bash
git checkout -b firstname/feature-name
```

For example:
```bash
git checkout -b john/user-authentication
git checkout -b sarah/promotion-api
```

3. Make your changes and commit them:

```bash
git add .
git commit -m "Descriptive commit message"
```

4. Push your branch to the remote repository:

```bash
git push origin firstname/feature-name
```

5. Create a Pull Request on GitHub when your feature is complete.

### Pull Request Process

1. Ensure your code follows the project's coding standards
2. Update the README.md with details of changes if applicable
3. The PR requires approval from at least one reviewer
4. Once approved, your changes will be merged into the main branch

## API Documentation

API documentation is available at `/api/docs/` when the server is running.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

Created by Emmanuel Adubi

