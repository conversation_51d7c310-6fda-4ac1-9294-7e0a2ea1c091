from rest_framework.routers import DefaultRouter
from .views import (
    SchemeViewSet, SchemeTargetViewSet, SchemeRegistrationViewSet,
    TransactionViewSet, TransactionItemViewSet, RewardViewSet
)
from django.urls import path, include

router = DefaultRouter()
router.register(r'schemes', SchemeViewSet)
router.register(r'scheme-targets', SchemeTargetViewSet)
router.register(r'scheme-registrations', SchemeRegistrationViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'transaction-items', TransactionItemViewSet)
router.register(r'rewards', RewardViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
