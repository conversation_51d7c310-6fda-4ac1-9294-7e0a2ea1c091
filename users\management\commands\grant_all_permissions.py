from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.apps import apps

User = get_user_model()

class Command(BaseCommand):
    help = 'Grants all permissions to superusers and ensures proper permission setup'

    def handle(self, *args, **options):
        # Create permissions for all models if they don't exist
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                content_type = ContentType.objects.get_for_model(model)
                for action in ['add', 'change', 'delete', 'view']:
                    codename = f"{action}_{model._meta.model_name}"
                    name = f"Can {action} {model._meta.verbose_name}"
                    
                    Permission.objects.get_or_create(
                        codename=codename,
                        content_type=content_type,
                        defaults={'name': name}
                    )
                    
        # Grant all permissions to superusers
        superusers = User.objects.filter(is_superuser=True)
        all_permissions = Permission.objects.all()
        
        for user in superusers:
            # Ensure user is staff
            if not user.is_staff:
                user.is_staff = True
                user.save()
                self.stdout.write(self.style.SUCCESS(f"Made {user.username} a staff user"))
                
            # Grant all permissions
            user.user_permissions.add(*all_permissions)
            self.stdout.write(self.style.SUCCESS(f"Granted all permissions to {user.username}"))
            
            # Add to all groups
            for group in Group.objects.all():
                user.groups.add(group)
                
            self.stdout.write(self.style.SUCCESS(f"Successfully set up {user.username} with all permissions"))
