# Generated by Django 5.1.1 on 2025-07-17 12:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('territories', '0001_initial'),
        ('users', '0003_phoneotp'),
    ]

    operations = [
        migrations.CreateModel(
            name='Distributor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('distributor_name', models.CharField(max_length=100)),
                ('distributor_code', models.CharField(max_length=50, unique=True)),
                ('distributor_territory_name', models.CharField(max_length=100)),
                ('distributor_owner_name', models.Char<PERSON>ield(max_length=100)),
                ('territory_manager', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('territory', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='distributors', to='territories.territory')),
            ],
        ),
        migrations.AddField(
            model_name='customer',
            name='distributor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='customers', to='users.distributor'),
        ),
        migrations.CreateModel(
            name='DSR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('employee_code', models.CharField(max_length=50, unique=True)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('distributor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dsrs', to='users.distributor')),
                ('territory', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='dsrs', to='territories.territory')),
            ],
        ),
        migrations.AddField(
            model_name='customer',
            name='dsr',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='customers', to='users.dsr'),
        ),
    ]
