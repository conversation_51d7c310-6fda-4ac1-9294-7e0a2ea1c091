from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import Customer

User = get_user_model()

class Command(BaseCommand):
    help = 'Display admin and sample user credentials for the frontend admin portal'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== ADMIN LOGIN CREDENTIALS ==='))
        self.stdout.write('Use these credentials to login to the admin portal:\n')
        
        # Get admin users
        admin_users = User.objects.filter(is_staff=True).order_by('-is_superuser', 'username')
        
        if not admin_users.exists():
            self.stdout.write(self.style.ERROR('No admin users found. Run: python manage.py seed_admin_users'))
            return
        
        # Default passwords (these should match what's in seed_admin_users.py)
        default_passwords = {
            'superadmin': 'Admin@123',
            'admin': 'Admin@456',
            'national_manager': 'National@789',
            'regional_manager': 'Regional@101'
        }
        
        for user in admin_users:
            password = default_passwords.get(user.username, 'Password not available')
            role = user.role.get_name_display() if user.role else 'No Role'
            
            self.stdout.write(f"Role: {role}")
            self.stdout.write(f"Username: {user.username}")
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"Password: {password}")
            self.stdout.write(f"Superuser: {'Yes' if user.is_superuser else 'No'}")
            self.stdout.write('-' * 50)
        
        self.stdout.write(self.style.WARNING('\nAdmin Portal URLs:'))
        self.stdout.write('Django Admin: http://localhost:8000/admin/')
        self.stdout.write('API Documentation: http://localhost:8000/swagger/')
        self.stdout.write('API Root: http://localhost:8000/api/')
        
        # Show sample customers
        self.stdout.write(self.style.WARNING('\nSample Customer Credentials:'))
        sample_customers = Customer.objects.all()[:3]
        
        for customer in sample_customers:
            self.stdout.write(f"Customer: {customer.customer_code} - {customer.username}")
            self.stdout.write(f"Email: {customer.email}")
            self.stdout.write(f"Password: Customer@123")
            self.stdout.write(f"Channel: {customer.channel}")
            self.stdout.write(f"Type: {customer.customer_type}")
            if customer.distributor:
                self.stdout.write(f"Distributor: {customer.distributor.distributor_name}")
            if customer.dsr:
                self.stdout.write(f"DSR: {customer.dsr.name}")
            self.stdout.write('-' * 30)
        
        self.stdout.write(self.style.SUCCESS('\n=== QUICK ACCESS COMMANDS ==='))
        self.stdout.write('Reset admin users: python manage.py seed_admin_users --reset')
        self.stdout.write('Show credentials: python manage.py show_admin_credentials')
        self.stdout.write('Run server: python manage.py runserver')
