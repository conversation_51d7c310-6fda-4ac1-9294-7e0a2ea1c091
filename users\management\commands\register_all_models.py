from django.core.management.base import BaseCommand
from django.apps import apps
from django.contrib import admin
from innov8.admin import admin_site

class Command(BaseCommand):
    help = 'Registers all models with the admin site'

    def handle(self, *args, **options):
        # Get all models from all apps
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                try:
                    # Skip if already registered
                    if model in admin_site._registry:
                        self.stdout.write(f"Model {model.__name__} already registered")
                        continue
                        
                    # Register the model with a basic ModelAdmin
                    admin_site.register(model)
                    self.stdout.write(self.style.SUCCESS(f"Registered {model.__name__} with admin site"))
                except admin.sites.AlreadyRegistered:
                    self.stdout.write(f"Model {model.__name__} already registered")


