from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserRole, Customer, Distributor, DSR
from innov8.admin import admin_site

@admin_site.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')

@admin_site.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('email', 'username', 'first_name', 'last_name', 'role', 'customer_code', 'is_staff')
    list_filter = ('role', 'is_staff', 'is_active', 'region', 'area', 'territory')
    search_fields = ('email', 'username', 'first_name', 'last_name', 'customer_code')
    
    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number')}),
        ('Role & Territory', {'fields': ('role', 'region', 'area', 'territory')}),
        ('Customer info', {'fields': ('customer_code', 'address', 'city')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'role'),
        }),
    )

@admin_site.register(Distributor)
class DistributorAdmin(admin.ModelAdmin):
    list_display = ('distributor_code', 'distributor_name', 'distributor_territory_name', 'distributor_owner_name', 'territory_manager', 'territory')
    list_filter = ('territory', 'territory__area', 'territory__area__region')
    search_fields = ('distributor_code', 'distributor_name', 'distributor_owner_name', 'territory_manager')

    fieldsets = (
        ('Basic Information', {
            'fields': ('distributor_name', 'distributor_code', 'distributor_territory_name')
        }),
        ('Management', {
            'fields': ('distributor_owner_name', 'territory_manager')
        }),
        ('Territory Mapping', {
            'fields': ('territory',)
        }),
    )

@admin_site.register(DSR)
class DSRAdmin(admin.ModelAdmin):
    list_display = ('employee_code', 'name', 'distributor', 'territory', 'phone_number', 'email', 'is_active')
    list_filter = ('is_active', 'distributor', 'territory', 'territory__area', 'territory__area__region')
    search_fields = ('employee_code', 'name', 'phone_number', 'email', 'distributor__distributor_name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'employee_code', 'phone_number', 'email')
        }),
        ('Assignment', {
            'fields': ('distributor', 'territory', 'is_active')
        }),
    )

@admin_site.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('customer_code', 'username', 'email', 'channel', 'customer_type', 'distributor', 'dsr')
    list_filter = ('channel', 'customer_type', 'distributor', 'dsr', 'is_active')
    search_fields = ('customer_code', 'username', 'email', 'first_name', 'last_name')

    fieldsets = (
        ('User Information', {
            'fields': ('username', 'email', 'first_name', 'last_name', 'phone_number')
        }),
        ('Customer Details', {
            'fields': ('customer_code', 'channel', 'customer_type', 'physical_address')
        }),
        ('Sales Assignment', {
            'fields': ('distributor', 'dsr')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'role')
        }),
    )

