from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import User<PERSON><PERSON>, Customer, Distributor, DSR
from territories.models import Region, Area, Territory
from brands.models import Brand, SKU
from django.utils import timezone
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Seed the database with admin users and sample data for frontend admin portal'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing admin users before creating new ones',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Deleting existing admin users...')
            User.objects.filter(is_superuser=True).delete()
            User.objects.filter(is_staff=True).delete()

        # Create User Roles
        self.create_user_roles()
        
        # Create Territories (needed for distributors and DSRs)
        self.create_territories()
        
        # Create Admin Users
        self.create_admin_users()
        
        # Create Sample Distributors and DSRs
        self.create_distributors_and_dsrs()
        
        # Create Sample Customers
        self.create_sample_customers()
        
        self.stdout.write(self.style.SUCCESS('\n=== ADMIN SEEDING COMPLETE ==='))
        self.display_credentials()

    def create_user_roles(self):
        """Create all user roles"""
        self.stdout.write('Creating user roles...')
        roles_created = 0
        
        for role_name, role_display in UserRole.ROLE_CHOICES:
            role, created = UserRole.objects.get_or_create(
                name=role_name,
                defaults={'description': f'{role_display} role'}
            )
            if created:
                roles_created += 1
        
        self.stdout.write(self.style.SUCCESS(f'✓ Created {roles_created} user roles'))

    def create_territories(self):
        """Create sample territories"""
        self.stdout.write('Creating territories...')
        
        # Create Regions
        regions_data = [
            {'name': 'Lagos Region', 'code': 'LG', 'description': 'Lagos State Region'},
            {'name': 'Abuja Region', 'code': 'AB', 'description': 'Federal Capital Territory'},
            {'name': 'Kano Region', 'code': 'KN', 'description': 'Northern Region'},
        ]
        
        regions = []
        for region_data in regions_data:
            region, created = Region.objects.get_or_create(
                code=region_data['code'],
                defaults=region_data
            )
            regions.append(region)
        
        # Create Areas
        areas_data = [
            {'name': 'Lagos Island', 'code': 'LI', 'region': regions[0]},
            {'name': 'Lagos Mainland', 'code': 'LM', 'region': regions[0]},
            {'name': 'Abuja Central', 'code': 'AC', 'region': regions[1]},
            {'name': 'Kano Central', 'code': 'KC', 'region': regions[2]},
        ]
        
        areas = []
        for area_data in areas_data:
            area, created = Area.objects.get_or_create(
                code=area_data['code'],
                defaults=area_data
            )
            areas.append(area)
        
        # Create Territories
        territories_data = [
            {'name': 'Victoria Island', 'code': 'VI', 'area': areas[0]},
            {'name': 'Ikoyi', 'code': 'IK', 'area': areas[0]},
            {'name': 'Ikeja', 'code': 'IJ', 'area': areas[1]},
            {'name': 'Surulere', 'code': 'SU', 'area': areas[1]},
            {'name': 'Wuse', 'code': 'WU', 'area': areas[2]},
            {'name': 'Sabon Gari', 'code': 'SG', 'area': areas[3]},
        ]
        
        for territory_data in territories_data:
            Territory.objects.get_or_create(
                code=territory_data['code'],
                defaults=territory_data
            )
        
        self.stdout.write(self.style.SUCCESS(f'✓ Created territories structure'))

    def create_admin_users(self):
        """Create admin users with different roles"""
        self.stdout.write('Creating admin users...')
        
        admin_role = UserRole.objects.get(name='ADMIN')
        national_role = UserRole.objects.get(name='NATIONAL')
        regional_role = UserRole.objects.get(name='REGIONAL')
        
        admin_users = [
            {
                'username': 'superadmin',
                'email': '<EMAIL>',
                'password': 'Admin@123',
                'first_name': 'Super',
                'last_name': 'Administrator',
                'role': admin_role,
                'is_superuser': True,
                'is_staff': True,
                'phone_number': '+234-************'
            },
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'Admin@456',
                'first_name': 'System',
                'last_name': 'Admin',
                'role': admin_role,
                'is_superuser': False,
                'is_staff': True,
                'phone_number': '+234-************'
            },
            {
                'username': 'national_manager',
                'email': '<EMAIL>',
                'password': 'National@789',
                'first_name': 'National',
                'last_name': 'Manager',
                'role': national_role,
                'is_superuser': False,
                'is_staff': True,
                'phone_number': '+234-************'
            },
            {
                'username': 'regional_manager',
                'email': '<EMAIL>',
                'password': 'Regional@101',
                'first_name': 'Regional',
                'last_name': 'Manager',
                'role': regional_role,
                'is_superuser': False,
                'is_staff': True,
                'phone_number': '+234-************'
            }
        ]
        
        self.admin_credentials = []
        
        for user_data in admin_users:
            password = user_data.pop('password')
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            
            if created:
                user.set_password(password)
                user.save()
                self.admin_credentials.append({
                    'username': user.username,
                    'email': user.email,
                    'password': password,
                    'role': user.role.get_name_display() if user.role else 'No Role'
                })
        
        self.stdout.write(self.style.SUCCESS(f'✓ Created {len(self.admin_credentials)} admin users'))

    def create_distributors_and_dsrs(self):
        """Create sample distributors and DSRs"""
        self.stdout.write('Creating distributors and DSRs...')
        
        territories = list(Territory.objects.all())
        
        distributors_data = [
            {
                'distributor_name': 'Lagos Premium Distributors Ltd',
                'distributor_code': 'LPD001',
                'distributor_territory_name': 'Lagos Island Territory',
                'distributor_owner_name': 'Adebayo Johnson',
                'territory_manager': 'Funmi Adeyemi',
                'territory': territories[0] if territories else None
            },
            {
                'distributor_name': 'Mainland Distribution Network',
                'distributor_code': 'MDN002',
                'distributor_territory_name': 'Lagos Mainland Territory',
                'distributor_owner_name': 'Chidi Okafor',
                'territory_manager': 'Kemi Ogundimu',
                'territory': territories[1] if len(territories) > 1 else None
            },
            {
                'distributor_name': 'Abuja Central Distributors',
                'distributor_code': 'ACD003',
                'distributor_territory_name': 'FCT Central Territory',
                'distributor_owner_name': 'Ibrahim Musa',
                'territory_manager': 'Aisha Mohammed',
                'territory': territories[2] if len(territories) > 2 else None
            }
        ]
        
        distributors = []
        for dist_data in distributors_data:
            distributor, created = Distributor.objects.get_or_create(
                distributor_code=dist_data['distributor_code'],
                defaults=dist_data
            )
            distributors.append(distributor)
        
        # Create DSRs for each distributor
        dsr_names = [
            'Tunde Bakare', 'Ngozi Okwu', 'Yusuf Garba', 'Blessing Eze',
            'Emeka Nwosu', 'Fatima Aliyu', 'Segun Adebisi', 'Chioma Obi'
        ]
        
        dsr_count = 0
        for i, distributor in enumerate(distributors):
            # Create 2-3 DSRs per distributor
            num_dsrs = random.randint(2, 3)
            for j in range(num_dsrs):
                if dsr_count < len(dsr_names):
                    dsr_data = {
                        'name': dsr_names[dsr_count],
                        'employee_code': f'DSR{str(dsr_count + 1).zfill(3)}',
                        'phone_number': f'+234-80{random.randint(1000000, 9999999)}',
                        'email': f'dsr{dsr_count + 1}@unilever.com',
                        'distributor': distributor,
                        'territory': distributor.territory,
                        'is_active': True
                    }
                    
                    DSR.objects.get_or_create(
                        employee_code=dsr_data['employee_code'],
                        defaults=dsr_data
                    )
                    dsr_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'✓ Created {len(distributors)} distributors and {dsr_count} DSRs'))

    def create_sample_customers(self):
        """Create sample customers"""
        self.stdout.write('Creating sample customers...')
        
        distributors = list(Distributor.objects.all())
        dsrs = list(DSR.objects.all())
        
        customers_data = [
            {
                'username': 'shoprite_vi',
                'email': '<EMAIL>',
                'first_name': 'Shoprite',
                'last_name': 'Victoria Island',
                'customer_code': 'CUST001',
                'channel': 'SUPERMARKET',
                'customer_type': 'diamond',
                'physical_address': '1 Tiamiyu Savage Street, Victoria Island, Lagos',
                'phone_number': '+234-8012345001'
            },
            {
                'username': 'spar_ikeja',
                'email': '<EMAIL>',
                'first_name': 'SPAR',
                'last_name': 'Ikeja',
                'customer_code': 'CUST002',
                'channel': 'SUPERMARKET',
                'customer_type': 'gold',
                'physical_address': '45 Isaac John Street, Ikeja, Lagos',
                'phone_number': '+234-8012345002'
            },
            {
                'username': 'mama_cass_store',
                'email': '<EMAIL>',
                'first_name': 'Mama Cass',
                'last_name': 'Provisions',
                'customer_code': 'CUST003',
                'channel': 'NLS',
                'customer_type': 'silver',
                'physical_address': '23 Market Street, Surulere, Lagos',
                'phone_number': '+234-8012345003'
            }
        ]
        
        for i, customer_data in enumerate(customers_data):
            # Assign distributor and DSR
            if distributors:
                customer_data['distributor'] = distributors[i % len(distributors)]
            if dsrs:
                customer_data['dsr'] = dsrs[i % len(dsrs)]
            
            customer, created = Customer.objects.get_or_create(
                customer_code=customer_data['customer_code'],
                defaults=customer_data
            )
            
            if created:
                customer.set_password('Customer@123')
                customer.save()
        
        self.stdout.write(self.style.SUCCESS(f'✓ Created {len(customers_data)} sample customers'))

    def display_credentials(self):
        """Display all admin credentials"""
        self.stdout.write(self.style.SUCCESS('\n=== ADMIN LOGIN CREDENTIALS ==='))
        self.stdout.write('Use these credentials to login to the admin portal:\n')
        
        for cred in self.admin_credentials:
            self.stdout.write(f"Role: {cred['role']}")
            self.stdout.write(f"Username: {cred['username']}")
            self.stdout.write(f"Email: {cred['email']}")
            self.stdout.write(f"Password: {cred['password']}")
            self.stdout.write('-' * 40)
        
        self.stdout.write(self.style.WARNING('\nAdmin Portal URLs:'))
        self.stdout.write('Django Admin: http://localhost:8000/admin/')
        self.stdout.write('API Documentation: http://localhost:8000/swagger/')
        
        self.stdout.write(self.style.WARNING('\nSample Customer Credentials:'))
        self.stdout.write('Username: shoprite_vi, Password: Customer@123')
        self.stdout.write('Username: spar_ikeja, Password: Customer@123')
        self.stdout.write('Username: mama_cass_store, Password: Customer@123')
