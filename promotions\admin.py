from django.contrib import admin
from .models import Scheme, SchemeTarget, SchemeRegistration, Transaction, TransactionItem, Reward
from innov8.admin import admin_site

@admin_site.register(Scheme)
class SchemeAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_active')
    list_filter = ('is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    filter_horizontal = ('brands', 'skus', 'territories')

@admin_site.register(SchemeTarget)
class SchemeTargetAdmin(admin.ModelAdmin):
    list_display = ('scheme', 'customer', 'target_amount', 'achieved_amount', 'achievement_percentage')
    list_filter = ('scheme',)
    search_fields = ('customer__email', 'customer__username', 'scheme__name')

@admin_site.register(SchemeRegistration)
class SchemeRegistrationAdmin(admin.ModelAdmin):
    list_display = ('scheme', 'customer', 'registration_date')
    list_filter = ('scheme', 'registration_date')
    search_fields = ('customer__email', 'customer__username', 'scheme__name')

@admin_site.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('customer', 'scheme', 'transaction_type', 'transaction_date', 'total_amount')
    list_filter = ('transaction_type', 'scheme', 'transaction_date')
    search_fields = ('customer__email', 'customer__username', 'invoice_number')

@admin_site.register(TransactionItem)
class TransactionItemAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'sku', 'quantity', 'unit_price', 'total_price')
    list_filter = ('sku', 'transaction__transaction_type')
    search_fields = ('sku__code', 'sku__name', 'transaction__invoice_number')

@admin_site.register(Reward)
class RewardAdmin(admin.ModelAdmin):
    list_display = ('scheme', 'customer', 'amount', 'status', 'earned_date', 'payment_date')
    list_filter = ('status', 'scheme', 'earned_date', 'payment_date')
    search_fields = ('customer__email', 'customer__username', 'scheme__name')

