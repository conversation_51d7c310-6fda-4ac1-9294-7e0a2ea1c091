from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import UserRole

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix admin user permissions to ensure they can login to the admin portal'

    def handle(self, *args, **options):
        self.stdout.write('Fixing admin user permissions...')
        
        # List of usernames that should have admin access
        admin_usernames = [
            'superadmin', 'admin', 'national_manager', 'regional_manager'
        ]
        
        fixed_count = 0
        
        for username in admin_usernames:
            try:
                user = User.objects.get(username=username)
                
                # Ensure user is active and staff
                updated = False
                if not user.is_active:
                    user.is_active = True
                    updated = True
                    self.stdout.write(f'✓ Activated user: {username}')
                
                if not user.is_staff:
                    user.is_staff = True
                    updated = True
                    self.stdout.write(f'✓ Made user staff: {username}')
                
                # Set superuser for main admins
                if username in ['superadmin', 'admin'] and not user.is_superuser:
                    user.is_superuser = True
                    updated = True
                    self.stdout.write(f'✓ Made user superuser: {username}')
                
                if updated:
                    user.save()
                    fixed_count += 1
                
                # Display current status
                self.stdout.write(
                    f'User: {username} - Active: {user.is_active}, '
                    f'Staff: {user.is_staff}, Superuser: {user.is_superuser}, '
                    f'Role: {user.role.get_name_display() if user.role else "No Role"}'
                )
                
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'User not found: {username}')
                )
        
        # Also check for any users with empty usernames and fix them
        empty_username_users = User.objects.filter(username='')
        for user in empty_username_users:
            if user.email:
                # Use email prefix as username
                new_username = user.email.split('@')[0]
                user.username = new_username
                user.save()
                self.stdout.write(f'✓ Fixed empty username for user with email: {user.email} -> {new_username}')
                fixed_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'\nFixed {fixed_count} user permission issues')
        )
        
        # Test authentication for main admin users
        self.stdout.write('\nTesting authentication...')
        test_credentials = [
            ('superadmin', 'Admin@123'),
            ('admin', 'Admin@456'),
            ('national_manager', 'National@789'),
            ('regional_manager', 'Regional@101')
        ]
        
        from django.contrib.auth import authenticate
        
        for username, password in test_credentials:
            try:
                user = authenticate(username=username, password=password)
                if user:
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Authentication successful: {username}')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'✗ Authentication failed: {username}')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Authentication error for {username}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS('\nAdmin permission fix complete!')
        )
