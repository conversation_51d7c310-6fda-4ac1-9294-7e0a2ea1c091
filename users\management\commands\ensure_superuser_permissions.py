from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission

User = get_user_model()

class Command(BaseCommand):
    help = 'Ensures that superusers have all permissions'

    def handle(self, *args, **options):
        superusers = User.objects.filter(is_superuser=True)
        all_permissions = Permission.objects.all()
        
        for user in superusers:
            user.user_permissions.add(*all_permissions)
            self.stdout.write(self.style.SUCCESS(f'Successfully granted all permissions to {user.username}'))