# Generated by Django 5.1.1 on 2025-06-25 17:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='customer_code',
        ),
        migrations.RemoveField(
            model_name='user',
            name='address',
        ),
        migrations.RemoveField(
            model_name='user',
            name='area',
        ),
        migrations.RemoveField(
            model_name='user',
            name='city',
        ),
        migrations.RemoveField(
            model_name='user',
            name='region',
        ),
        migrations.RemoveField(
            model_name='user',
            name='territory',
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='users', to='users.userrole'),
        ),
        migrations.AlterField(
            model_name='userrole',
            name='name',
            field=models.CharField(choices=[('ADMIN', 'Administrator'), ('NATIONAL', 'National Manager'), ('REGIONAL', 'Regional Manager'), ('AREA', 'Area Manager'), ('TERRITORY', 'Territory Manager'), ('NORMAL', 'Normal User')], max_length=20, unique=True),
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('customer_code', models.CharField(max_length=50, unique=True)),
                ('channel', models.CharField(choices=[('OMLS', 'OMLS'), ('NLS', 'NLS'), ('WHS', 'WHS'), ('SUB D', 'SUB D'), ('SUPERMARKET', 'SUPERMARKET')], max_length=20)),
                ('physical_address', models.TextField(blank=True)),
                ('customer_type', models.CharField(choices=[('gold', 'Gold'), ('silver', 'Silver'), ('diamond', 'Diamond')], max_length=20)),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            bases=('users.user',),
        ),
    ]
