from django.contrib.admin import AdminSite
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType

class Innov8AdminSite(AdminSite):
    # Text to put at the end of each page's <title>
    site_title = "Project Innov8"
    
    # Text to put in each page's <h1> (and above login form)
    site_header = "Project Innov8"
    
    # Text to put at the top of the admin index page
    index_title = "Innov8 Administration"
    
    # URL for the "View site" link at the top of each admin page
    site_url = "/"
    
    def has_permission(self, request):
        """
        Always return True for superusers regardless of other permission settings
        """
        if request.user.is_superuser:
            return True
        return request.user.is_active and request.user.is_staff

# Instantiate the custom admin site
admin_site = Innov8AdminSite(name='innov8_admin')

# Register built-in models
admin_site.register(Permission)
admin_site.register(Group)
admin_site.register(ContentType)


