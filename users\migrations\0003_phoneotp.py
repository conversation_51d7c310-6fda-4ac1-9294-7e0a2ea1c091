# Generated by Django 5.1.1 on 2025-06-26 11:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_remove_user_customer_code_remove_user_address_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PhoneOTP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.<PERSON>r<PERSON><PERSON>(max_length=20)),
                ('otp', models.Char<PERSON>ield(max_length=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_verified', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
        ),
    ]
