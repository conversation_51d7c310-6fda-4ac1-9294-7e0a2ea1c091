from django.contrib import admin
from .models import Region, Area, Territory
from innov8.admin import admin_site

@admin_site.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')

@admin_site.register(Area)
class AreaAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'region')
    list_filter = ('region',)
    search_fields = ('name', 'code')

@admin_site.register(Territory)
class TerritoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'area')
    list_filter = ('area', 'area__region')
    search_fields = ('name', 'code')

