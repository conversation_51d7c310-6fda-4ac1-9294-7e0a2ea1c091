from django.db import models

class Region(models.Model):
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name

class Area(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='areas')
    description = models.TextField(blank=True)
    
    class Meta:
        unique_together = ('name', 'region')
    
    def __str__(self):
        return f"{self.name} ({self.region.name})"

class Territory(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    area = models.ForeignKey(Area, on_delete=models.CASCADE, related_name='territories')
    description = models.TextField(blank=True)
    
    class Meta:
        unique_together = ('name', 'area')
        verbose_name_plural = 'Territories'
    
    def __str__(self):
        return f"{self.name} ({self.area.name}, {self.area.region.name})"
