from rest_framework import serializers
from .models import User, UserR<PERSON>, PhoneOT<PERSON>, Customer, Distributor, DSR

class UserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = '__all__'

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = '__all__'

class DistributorSerializer(serializers.ModelSerializer):
    territory_name = serializers.CharField(source='territory.name', read_only=True)

    class Meta:
        model = Distributor
        fields = '__all__'

class DSRSerializer(serializers.ModelSerializer):
    distributor_name = serializers.CharField(source='distributor.distributor_name', read_only=True)
    territory_name = serializers.CharField(source='territory.name', read_only=True)

    class Meta:
        model = DSR
        fields = '__all__'

class CustomerSerializer(serializers.ModelSerializer):
    distributor_name = serializers.CharField(source='distributor.distributor_name', read_only=True)
    dsr_name = serializers.Char<PERSON>ield(source='dsr.name', read_only=True)

    class Meta:
        model = Customer
        fields = '__all__'

class PhoneOTPRequestSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=20)

class PhoneOTPVerifySerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=20)
    otp = serializers.CharField(max_length=6)
