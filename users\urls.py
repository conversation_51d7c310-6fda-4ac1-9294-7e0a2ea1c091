from rest_framework.routers import DefaultRouter
from .views import (
    UserViewSet, UserRoleViewSet, PhoneOTPRequestView, PhoneOTPVerifyView,
    CustomerViewSet, DistributorViewSet, DSRViewSet
)
from django.urls import path, include

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'userroles', UserRoleViewSet)
router.register(r'customers', CustomerViewSet)
router.register(r'distributors', DistributorViewSet)
router.register(r'dsrs', DSRViewSet)

urlpatterns = [
    path('auth/request-otp/', PhoneOTPRequestView.as_view(), name='request-otp'),
    path('auth/verify-otp/', PhoneOTPVerifyView.as_view(), name='verify-otp'),
    path('', include(router.urls)),
]
