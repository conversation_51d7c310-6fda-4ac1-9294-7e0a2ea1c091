from rest_framework.routers import DefaultRouter
from .views import (
    UserViewSet, UserRoleViewSet, PhoneOTPRequestView, PhoneOTPVerifyView,
    CustomerViewSet, DistributorViewSet, DSRViewSet,
    LoginView, LogoutView, CSRFTokenView, CurrentUserView
)
from django.urls import path, include

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'userroles', UserRoleViewSet)
router.register(r'customers', CustomerViewSet)
router.register(r'distributors', DistributorViewSet)
router.register(r'dsrs', DSRViewSet)

urlpatterns = [
    # Authentication endpoints
    path('auth/login/', LoginView.as_view(), name='login'),
    path('auth/logout/', LogoutView.as_view(), name='logout'),
    path('auth/csrf/', CSRFTokenView.as_view(), name='csrf'),
    path('users/me/', CurrentUserView.as_view(), name='current-user'),

    # OTP endpoints
    path('auth/request-otp/', PhoneOTPRequestView.as_view(), name='request-otp'),
    path('auth/verify-otp/', PhoneOTPVerifyView.as_view(), name='verify-otp'),

    # Router URLs
    path('', include(router.urls)),
]
