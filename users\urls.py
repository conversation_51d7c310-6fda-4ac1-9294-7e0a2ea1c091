from rest_framework.routers import DefaultRouter
from .views import UserViewSet, UserRoleViewSet, PhoneOTPRequestView, PhoneOTPVerifyView
from django.urls import path, include

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'userroles', UserRoleViewSet)

urlpatterns = [
    path('auth/request-otp/', PhoneOTPRequestView.as_view(), name='request-otp'),
    path('auth/verify-otp/', PhoneOTPVerifyView.as_view(), name='verify-otp'),
    path('', include(router.urls)),
]
