from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
import pandas as pd
from .models import Scheme, SchemeTarget, SchemeRegistration, Transaction, TransactionItem, Reward
from .serializers import (
    SchemeSerializer, SchemeTargetSerializer, SchemeRegistrationSerializer,
    TransactionSerializer, TransactionItemSerializer, RewardSerializer
)
from drf_yasg.utils import swagger_auto_schema

class SchemeViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing schemes."""
    queryset = Scheme.objects.all()
    serializer_class = SchemeSerializer
    parser_classes = [MultiPartParser]

    @swagger_auto_schema(operation_description="Upload schemes in bulk from Excel file.")
    @action(detail=False, methods=['post'], url_path='upload-excel')
    def upload_excel(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file uploaded.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            df = pd.read_excel(file)
            for _, row in df.iterrows():
                Scheme.objects.update_or_create(
                    name=row['name'],
                    defaults={
                        'description': row.get('description', ''),
                        'start_date': row.get('start_date'),
                        'end_date': row.get('end_date'),
                        'is_active': row.get('is_active', True)
                    }
                )
            return Response({'status': 'Schemes uploaded successfully.'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class SchemeTargetViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing scheme targets."""
    queryset = SchemeTarget.objects.all()
    serializer_class = SchemeTargetSerializer

class SchemeRegistrationViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing scheme registrations."""
    queryset = SchemeRegistration.objects.all()
    serializer_class = SchemeRegistrationSerializer

class TransactionViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing transactions."""
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer

class TransactionItemViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing transaction items."""
    queryset = TransactionItem.objects.all()
    serializer_class = TransactionItemSerializer

class RewardViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing rewards."""
    queryset = Reward.objects.all()
    serializer_class = RewardSerializer
