# Project Innov8 Frontend User Stories

This document outlines the user stories for the Project Innov8 frontend application. These stories are designed to help frontend engineers understand the requirements and functionality needed to implement the application.

## Authentication & User Management

### User Story: Authentication
**As a** user of the Innov8 platform  
**I want to** securely log in to the system  
**So that** I can access features based on my role and permissions

**Acceptance Criteria:**
- Users can log in with email/username and password
- Authentication uses JWT tokens stored securely
- Unauthorized access attempts redirect to login page
- Session timeout after period of inactivity
- Password reset functionality is available

**API Endpoints:**
- `POST /api/auth/login/` - Login with credentials
- `POST /api/auth/logout/` - Logout current user
- `GET /api/auth/me/` - Get current user details
- `POST /api/auth/password-reset/` - Request password reset
- `POST /api/auth/password-reset/confirm/` - Confirm password reset

### User Story: User Profile Management
**As a** logged-in user  
**I want to** view and edit my profile information  
**So that** my contact details and preferences are up to date

**Acceptance Criteria:**
- Users can view their profile information
- Users can update personal details (name, phone, etc.)
- Profile changes are validated before submission
- Success/error messages are displayed after operations

**API Endpoints:**
- `GET /api/users/me/` - Get current user profile
- `PUT /api/users/me/` - Update current user profile

## Territory Management

### User Story: Territory Hierarchy Visualization
**As a** manager or administrator  
**I want to** view the hierarchical structure of territories  
**So that** I can understand the geographical organization of our business

**Acceptance Criteria:**
- Display regions, areas, and territories in a hierarchical view
- Allow filtering and searching of territories
- Show counts of customers/users in each territory
- Provide drill-down capability from region to area to territory

**API Endpoints:**
- `GET /api/territories/regions/` - List all regions
- `GET /api/territories/regions/{id}/` - Get region details
- `GET /api/territories/areas/` - List all areas (with filter options)
- `GET /api/territories/areas/{id}/` - Get area details
- `GET /api/territories/territories/` - List all territories (with filter options)
- `GET /api/territories/territories/{id}/` - Get territory details

### User Story: Territory Assignment
**As an** administrator  
**I want to** assign users to specific territories  
**So that** they can manage customers and promotions in their assigned areas

**Acceptance Criteria:**
- Administrators can assign users to regions, areas, or territories
- Users can only be assigned to one geographical level
- The interface prevents invalid assignments
- Changes to assignments are logged

**API Endpoints:**
- `PUT /api/users/{id}/` - Update user with territory assignment
- `GET /api/territories/territories/{id}/users/` - Get users assigned to a territory

## Brand & SKU Management

### User Story: Brand and SKU Catalog
**As a** product manager  
**I want to** manage the catalog of brands and SKUs  
**So that** they can be used in promotion schemes

**Acceptance Criteria:**
- View list of brands with filtering and sorting options
- Add, edit, and deactivate brands
- View SKUs associated with each brand
- Add, edit, and deactivate SKUs
- Upload product images for brands and SKUs

**API Endpoints:**
- `GET /api/brands/` - List all brands
- `POST /api/brands/` - Create a new brand
- `GET /api/brands/{id}/` - Get brand details
- `PUT /api/brands/{id}/` - Update brand
- `GET /api/brands/{id}/skus/` - Get SKUs for a brand
- `POST /api/skus/` - Create a new SKU
- `GET /api/skus/{id}/` - Get SKU details
- `PUT /api/skus/{id}/` - Update SKU

## Promotion Scheme Management

### User Story: Promotion Scheme Creation
**As a** marketing manager  
**I want to** create promotion schemes with specific criteria  
**So that** customers can participate and earn rewards

**Acceptance Criteria:**
- Create schemes with name, description, start/end dates
- Select brands and SKUs eligible for the scheme
- Define territories where the scheme is available
- Set scheme rules and reward calculations
- Preview and publish schemes

**API Endpoints:**
- `GET /api/promotions/schemes/` - List all schemes
- `POST /api/promotions/schemes/` - Create a new scheme
- `GET /api/promotions/schemes/{id}/` - Get scheme details
- `PUT /api/promotions/schemes/{id}/` - Update scheme
- `POST /api/promotions/schemes/{id}/publish/` - Publish scheme

### User Story: Customer Target Setting
**As a** marketing manager  
**I want to** set sales targets for customers in a scheme  
**So that** rewards are calculated based on achievement

**Acceptance Criteria:**
- Set individual targets for customers in a scheme
- Import targets from CSV/Excel
- View and edit targets for multiple customers
- Track target vs. achievement in real-time

**API Endpoints:**
- `GET /api/promotions/schemes/{id}/targets/` - List targets for a scheme
- `POST /api/promotions/schemes/{id}/targets/` - Create targets for customers
- `PUT /api/promotions/schemes/{id}/targets/{target_id}/` - Update a target
- `POST /api/promotions/schemes/{id}/targets/import/` - Import targets from file

### User Story: Transaction Recording
**As a** sales representative  
**I want to** record customer transactions for eligible schemes  
**So that** customers can earn rewards based on their purchases

**Acceptance Criteria:**
- Record transactions with invoice details
- Add multiple SKUs to a transaction
- Validate SKUs against scheme eligibility
- Calculate transaction value and contribution to targets
- Show real-time update of target achievement

**API Endpoints:**
- `POST /api/promotions/transactions/` - Create a new transaction
- `GET /api/promotions/transactions/` - List transactions (with filters)
- `GET /api/promotions/transactions/{id}/` - Get transaction details
- `POST /api/promotions/transactions/{id}/items/` - Add items to transaction

### User Story: Reward Management
**As a** finance manager  
**I want to** review and approve rewards earned by customers  
**So that** payments can be processed accurately

**Acceptance Criteria:**
- View list of earned rewards pending approval
- Review customer achievement details
- Approve or reject rewards with comments
- Mark rewards as paid
- Generate reward reports by scheme, territory, or time period

**API Endpoints:**
- `GET /api/promotions/rewards/` - List rewards (with filters)
- `GET /api/promotions/rewards/{id}/` - Get reward details
- `PUT /api/promotions/rewards/{id}/approve/` - Approve a reward
- `PUT /api/promotions/rewards/{id}/reject/` - Reject a reward
- `PUT /api/promotions/rewards/{id}/mark-paid/` - Mark reward as paid
- `GET /api/promotions/rewards/reports/` - Generate reward reports

## Dashboard & Analytics

### User Story: Performance Dashboard
**As a** manager  
**I want to** view key performance metrics on a dashboard  
**So that** I can monitor business performance at a glance

**Acceptance Criteria:**
- Display summary metrics for active schemes
- Show territory performance comparisons
- Display target vs. achievement charts
- Provide trend analysis over time
- Allow filtering by date range, territory, and scheme

**API Endpoints:**
- `GET /api/analytics/dashboard/` - Get dashboard summary data
- `GET /api/analytics/schemes/performance/` - Get scheme performance metrics
- `GET /api/analytics/territories/performance/` - Get territory performance
- `GET /api/analytics/trends/` - Get trend data

### User Story: Customer Performance
**As a** sales representative  
**I want to** view individual customer performance  
**So that** I can identify opportunities for growth

**Acceptance Criteria:**
- View customer purchase history
- See customer participation in schemes
- Track customer target achievement over time
- Compare customer performance against similar customers
- Identify top-performing customers

**API Endpoints:**
- `GET /api/users/{id}/transactions/` - Get customer transactions
- `GET /api/users/{id}/schemes/` - Get schemes customer is enrolled in
- `GET /api/users/{id}/performance/` - Get customer performance metrics
- `GET /api/analytics/customers/top/` - Get top-performing customers

## Mobile Responsiveness

### User Story: Mobile Access
**As a** field sales representative  
**I want to** access the system from my mobile device  
**So that** I can record transactions and check information while visiting customers

**Acceptance Criteria:**
- All critical features work on mobile devices
- Interface adapts to different screen sizes
- Forms are usable on touchscreens
- Offline capability for transaction recording
- Sync when connection is restored

**Technical Requirements:**
- Responsive design for all screens
- Progressive Web App capabilities
- Local storage for offline data
- Background sync when online

## Role-Based Access Control

### User Story: Role-Based Interface
**As a** user with a specific role  
**I want to** see only the features relevant to my role  
**So that** I can focus on my responsibilities without confusion

**Acceptance Criteria:**
- Menu items and features are filtered based on user role
- Administrators see all features
- Territory managers see only their territories
- Sales representatives see only their customers
- Attempt to access unauthorized features redirects appropriately

**API Endpoints:**
- `GET /api/auth/permissions/` - Get current user permissions
- Role information is included in the user profile

## Technical Requirements

1. **Authentication**: JWT-based authentication with secure token storage
2. **API Communication**: RESTful API calls using Axios
3. **State Management**: React Context or Redux for global state
4. **Form Handling**: Formik with Yup validation
5. **UI Components**: Material-UI component library
6. **Charting**: Chart.js for data visualization
7. **Responsive Design**: Mobile-first approach
8. **Error Handling**: Consistent error handling and user feedback
9. **Loading States**: Clear loading indicators for async operations
10. **Testing**: Jest and React Testing Library for component testing

---

Created by Emmanuel Adubi