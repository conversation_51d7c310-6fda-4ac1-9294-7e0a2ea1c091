# Innov8 API: Frontend Integration Guide

## Base API URL
- Local: http://127.0.0.1:8000/

## CORS
- CORS is enabled for all origins. You can call the API from any frontend URL (e.g., http://localhost:5173/).

## Authentication
- The API uses session and token authentication.
- For most endpoints, you must be authenticated.
- Obtain a token via the login endpoint or use session authentication if integrated with Django admin.

## API Documentation
- Swagger UI: http://127.0.0.1:8000/swagger/
- ReDoc: http://127.0.0.1:8000/redoc/
- These pages show all available endpoints, request/response formats, and parameters.

## Common Endpoints
- Users: `/api/users/`
- User Roles: `/api/users/roles/`
- Brands: `/api/brands/`
- SKUs: `/api/brands/skus/`
- Regions: `/api/territories/regions/`
- Areas: `/api/territories/areas/`
- Territories: `/api/territories/territories/`
- Schemes: `/api/promotions/schemes/`
- Scheme Targets: `/api/promotions/schemetargets/`
- Scheme Registrations: `/api/promotions/schemeregistrations/`
- Transactions: `/api/promotions/transactions/`
- Transaction Items: `/api/promotions/transactionitems/`
- Rewards: `/api/promotions/rewards/`

## Example: Fetch Users (with fetch API)
```js
fetch('http://127.0.0.1:8000/api/users/', {
  headers: {
    'Authorization': 'Token <your_token>',
    'Content-Type': 'application/json'
  }
})
  .then(res => res.json())
  .then(data => console.log(data));
```

## Notes
- All endpoints accept and return JSON.
- For POST/PUT requests, send data as JSON in the request body.
- For file uploads (e.g., Excel), use `multipart/form-data`.
- See Swagger UI for detailed schemas and try out requests interactively.

## Troubleshooting
- If you get a CORS error, ensure your frontend is running on a different port (e.g., 5173) and the backend is running.
- If you get a 401 Unauthorized, ensure you are authenticated and sending the correct token.

---
For more details, see the Swagger docs or contact the backend team.
