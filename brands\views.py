from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
import pandas as pd
from .models import Brand, SKU
from .serializers import BrandSerializer, SKUSerializer
from drf_yasg.utils import swagger_auto_schema

class BrandViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing brands."""
    queryset = Brand.objects.all()
    serializer_class = BrandSerializer
    parser_classes = [MultiPartParser]

    @swagger_auto_schema(operation_description="Upload brands in bulk from Excel file.")
    @action(detail=False, methods=['post'], url_path='upload-excel')
    def upload_excel(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file uploaded.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            df = pd.read_excel(file)
            for _, row in df.iterrows():
                Brand.objects.update_or_create(
                    code=row['code'],
                    defaults={
                        'name': row['name'],
                        'description': row.get('description', ''),
                        'is_active': row.get('is_active', True)
                    }
                )
            return Response({'status': 'Brands uploaded successfully.'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class SKUViewSet(viewsets.ModelViewSet):
    """API endpoint for viewing and editing SKUs."""
    queryset = SKU.objects.all()
    serializer_class = SKUSerializer
    parser_classes = [MultiPartParser]

    @swagger_auto_schema(operation_description="Upload SKUs in bulk from Excel file.")
    @action(detail=False, methods=['post'], url_path='upload-excel')
    def upload_excel(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file uploaded.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            df = pd.read_excel(file)
            for _, row in df.iterrows():
                brand = Brand.objects.get(code=row['brand_code'])
                SKU.objects.update_or_create(
                    code=row['code'],
                    defaults={
                        'name': row['name'],
                        'description': row.get('description', ''),
                        'brand': brand,
                        'price': row.get('price', 0.0),
                        'unit': row.get('unit', ''),
                        'is_active': row.get('is_active', True)
                    }
                )
            return Response({'status': 'SKUs uploaded successfully.'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
