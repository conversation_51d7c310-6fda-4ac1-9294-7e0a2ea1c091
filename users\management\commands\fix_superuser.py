from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Ensures that superusers have staff status'

    def handle(self, *args, **options):
        superusers = User.objects.filter(is_superuser=True)
        
        for user in superusers:
            if not user.is_staff:
                user.is_staff = True
                user.save()
                self.stdout.write(self.style.SUCCESS(f'Fixed staff status for {user.username}'))
            else:
                self.stdout.write(self.style.SUCCESS(f'{user.username} already has staff status'))