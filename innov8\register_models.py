from django.apps import apps
from django.contrib import admin
from .admin import admin_site

def register_models():
    """Register all models with the admin site"""
    # Get all models from all apps
    for app_config in apps.get_app_configs():
        # Skip Django's built-in apps
        if app_config.name.startswith('django.contrib'):
            continue
            
        for model in app_config.get_models():
            try:
                # Skip if already registered
                if model in admin_site._registry:
                    continue
                    
                # Register the model with a basic ModelAdmin
                admin_site.register(model)
                print(f"Registered {model.__name__} with admin site")
            except admin.sites.AlreadyRegistered:
                # Already registered, skip
                pass