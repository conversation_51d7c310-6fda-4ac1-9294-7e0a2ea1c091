from django.contrib import admin
from .models import Brand, SKU
from innov8.admin import admin_site

@admin_site.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('is_active',)

@admin_site.register(SKU)
class SKUAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'brand', 'is_active')
    list_filter = ('brand', 'is_active')
    search_fields = ('code', 'name')

