from django.core.management.base import BaseCommand
from users.models import User, UserRole
from brands.models import Brand, SKU, BrandCategory
from territories.models import Region, Area, Territory
from promotions.models import Scheme, SchemeTarget, SchemeRegistration, Transaction, TransactionItem, Reward
from django.utils import timezone
import random

class Command(BaseCommand):
    help = 'Seed the database with dummy data for all models.'

    def handle(self, *args, **options):
        # User Roles
        roles = []
        for role_name, _ in UserRole.ROLE_CHOICES:
            role, _ = UserRole.objects.get_or_create(name=role_name)
            roles.append(role)
        self.stdout.write(self.style.SUCCESS('Seeded UserRoles.'))

        # Users
        users = []
        for i in range(5):
            user, _ = User.objects.get_or_create(
                email=f'user{i}@example.com',
                defaults={
                    'username': f'user{i}',
                    'first_name': f'First{i}',
                    'last_name': f'Last{i}',
                    'role': random.choice(roles),
                    'is_active': True,
                    'is_staff': True
                }
            )
            users.append(user)
        self.stdout.write(self.style.SUCCESS('Seeded Users.'))

        # Brand Categories
        categories = []
        for i in range(5):
            cat, _ = BrandCategory.objects.get_or_create(
                name=f'Category{i}',
                defaults={'description': f'Description {i}'}
            )
            categories.append(cat)
        self.stdout.write(self.style.SUCCESS('Seeded BrandCategories.'))

        # Brands
        brands = []
        for i in range(5):
            brand, _ = Brand.objects.get_or_create(
                name=f'Brand{i}',
                defaults={
                    'category': random.choice(categories),
                    'description': f'Brand {i} description',
                    'is_active': True
                }
            )
            brands.append(brand)
        self.stdout.write(self.style.SUCCESS('Seeded Brands.'))

        # SKUs
        skus = []
        for i in range(5):
            sku, _ = SKU.objects.get_or_create(
                code=f'SKU{i}',
                defaults={
                    'name': f'SKU Name {i}',
                    'description': f'SKU {i} description',
                    'brand': random.choice(brands),
                    'list_price': random.uniform(10, 100),
                    'is_active': True
                }
            )
            skus.append(sku)
        self.stdout.write(self.style.SUCCESS('Seeded SKUs.'))

        # Regions
        regions = []
        for i in range(5):
            region, _ = Region.objects.get_or_create(
                name=f'Region{i}',
                defaults={'code': f'R{i}', 'description': f'Region {i}'}
            )
            regions.append(region)
        self.stdout.write(self.style.SUCCESS('Seeded Regions.'))

        # Areas
        areas = []
        for i in range(5):
            area, _ = Area.objects.get_or_create(
                name=f'Area{i}',
                defaults={'code': f'A{i}', 'region': random.choice(regions), 'description': f'Area {i}'}
            )
            areas.append(area)
        self.stdout.write(self.style.SUCCESS('Seeded Areas.'))

        # Territories
        territories = []
        for i in range(5):
            territory, _ = Territory.objects.get_or_create(
                name=f'Territory{i}',
                defaults={'code': f'T{i}', 'area': random.choice(areas), 'description': f'Territory {i}'}
            )
            territories.append(territory)
        self.stdout.write(self.style.SUCCESS('Seeded Territories.'))

        # Schemes
        schemes = []
        for i in range(5):
            scheme, _ = Scheme.objects.get_or_create(
                name=f'Scheme{i}',
                defaults={
                    'description': f'Scheme {i} description',
                    'start_date': timezone.now().date(),
                    'end_date': timezone.now().date(),
                    'is_active': True
                }
            )
            schemes.append(scheme)
        self.stdout.write(self.style.SUCCESS('Seeded Schemes.'))

        # SchemeTargets
        for i in range(5):
            SchemeTarget.objects.get_or_create(
                scheme=random.choice(schemes),
                customer=random.choice(users),
                defaults={
                    'target_amount': random.uniform(1000, 5000),
                    'achieved_amount': random.uniform(0, 1000)
                }
            )
        self.stdout.write(self.style.SUCCESS('Seeded SchemeTargets.'))

        # SchemeRegistrations
        for i in range(5):
            SchemeRegistration.objects.get_or_create(
                scheme=random.choice(schemes),
                customer=random.choice(users)
            )
        self.stdout.write(self.style.SUCCESS('Seeded SchemeRegistrations.'))

        # Transactions
        transactions = []
        for i in range(5):
            transaction, _ = Transaction.objects.get_or_create(
                customer=random.choice(users),
                scheme=random.choice(schemes),
                defaults={
                    'transaction_type': 'SALE',
                    'transaction_date': timezone.now(),
                    'invoice_number': f'INV{i}',
                    'total_amount': random.uniform(100, 1000)
                }
            )
            transactions.append(transaction)
        self.stdout.write(self.style.SUCCESS('Seeded Transactions.'))

        # TransactionItems
        for i in range(5):
            TransactionItem.objects.get_or_create(
                transaction=random.choice(transactions),
                sku=random.choice(skus),
                defaults={
                    'quantity': random.randint(1, 10),
                    'unit_price': random.uniform(10, 100)
                }
            )
        self.stdout.write(self.style.SUCCESS('Seeded TransactionItems.'))

        # Rewards
        for i in range(5):
            Reward.objects.get_or_create(
                scheme=random.choice(schemes),
                customer=random.choice(users),
                defaults={
                    'amount': random.uniform(10, 100),
                    'status': 'PENDING',
                    'earned_date': timezone.now().date(),
                    'description': f'Reward {i}'
                }
            )
        self.stdout.write(self.style.SUCCESS('Seeded Rewards.'))

        self.stdout.write(self.style.SUCCESS('Dummy data seeding complete!'))
